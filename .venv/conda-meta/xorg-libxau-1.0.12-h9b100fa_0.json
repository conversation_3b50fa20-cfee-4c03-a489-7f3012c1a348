{"name": "xorg-libxau", "version": "1.0.12", "build": "h9b100fa_0", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "subdir": "linux-64", "fn": "xorg-libxau-1.0.12-h9b100fa_0.conda", "md5": "a8005a9f6eb903e113cd5363e8a11459", "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/xorg-libxau-1.0.12-h9b100fa_0.conda", "sha256": "68acf87da69a0237d5083798a9cc9b89d2887a84a5323aa740006b53cd5159ef", "depends": ["libgcc-ng >=11.2.0"], "constrains": [], "license": "MIT", "timestamp": 1745958707000, "size": 13582, "requested_spec": "None", "package_tarball_full_path": "/root/miniconda3/pkgs/xorg-libxau-1.0.12-h9b100fa_0.conda", "extracted_package_dir": "/root/miniconda3/pkgs/xorg-libxau-1.0.12-h9b100fa_0", "files": ["include/X11/Xauth.h", "lib/libXau.so", "lib/libXau.so.6", "lib/libXau.so.6.0.0", "lib/pkgconfig/xau.pc"], "paths_data": {"paths_version": 1, "paths": [{"_path": "include/X11/Xauth.h", "path_type": "hardlink", "sha256": "d0bc0612c0d2eb38bf7519190ebb60ea0d538c86589a6fbc06e0d761ec22f9c4", "size_in_bytes": 4973, "sha256_in_prefix": "d0bc0612c0d2eb38bf7519190ebb60ea0d538c86589a6fbc06e0d761ec22f9c4"}, {"_path": "lib/libXau.so", "path_type": "softlink", "sha256": "63c07529fc685758e6fdae708d43e20eef99b9b26dc944e3985975ef1ef8c467", "size_in_bytes": 17232}, {"_path": "lib/libXau.so.6", "path_type": "softlink", "sha256": "63c07529fc685758e6fdae708d43e20eef99b9b26dc944e3985975ef1ef8c467", "size_in_bytes": 17232}, {"_path": "lib/libXau.so.6.0.0", "path_type": "hardlink", "sha256": "63c07529fc685758e6fdae708d43e20eef99b9b26dc944e3985975ef1ef8c467", "size_in_bytes": 17232, "sha256_in_prefix": "63c07529fc685758e6fdae708d43e20eef99b9b26dc944e3985975ef1ef8c467"}, {"_path": "lib/pkgconfig/xau.pc", "prefix_placeholder": "/croot/xorg-libxau_1745958680217/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "file_mode": "text", "path_type": "hardlink", "sha256": "a1d4b755e9a4fe7631bdfb421a1239dbf8d4d485fa67ae2fe181660fbfa8ec73", "size_in_bytes": 485, "sha256_in_prefix": "7d41d8b561bdde1738dc77fed5733eba02913f3a000dd6ed9afd9f45712e2906"}]}, "link": {"source": "/root/miniconda3/pkgs/xorg-libxau-1.0.12-h9b100fa_0", "type": 1}}