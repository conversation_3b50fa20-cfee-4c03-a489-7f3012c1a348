{"name": "_openmp_mutex", "version": "5.1", "build": "1_gnu", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "subdir": "linux-64", "fn": "_openmp_mutex-5.1-1_gnu.conda", "md5": "71d281e9c2192cb3fa425655a8defb85", "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/_openmp_mutex-5.1-1_gnu.conda", "sha256": "576011048d23f2e03372263493c5529f802286ff53e8426df99a5b11cc2572f3", "depends": ["_libgcc_mutex 0.1 main", "libgomp >=7.5.0"], "constrains": ["openmp_impl 9999"], "license": "BSD-3-<PERSON><PERSON>", "timestamp": 1652859733000, "size": 21315, "requested_spec": "None", "package_tarball_full_path": "/root/miniconda3/pkgs/_openmp_mutex-5.1-1_gnu.conda", "extracted_package_dir": "/root/miniconda3/pkgs/_openmp_mutex-5.1-1_gnu", "files": ["lib/libgomp.so.1"], "paths_data": {"paths_version": 1, "paths": [{"_path": "lib/libgomp.so.1", "path_type": "softlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}]}, "link": {"source": "/root/miniconda3/pkgs/_openmp_mutex-5.1-1_gnu", "type": 1}}