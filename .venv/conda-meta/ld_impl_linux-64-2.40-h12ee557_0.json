{"name": "ld_impl_linux-64", "version": "2.40", "build": "h12ee557_0", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "subdir": "linux-64", "fn": "ld_impl_linux-64-2.40-h12ee557_0.conda", "md5": "ee672b5f635340734f58d618b7bca024", "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/ld_impl_linux-64-2.40-h12ee557_0.conda", "sha256": "07137855558e3749fc88812644ab30fc543bc31bcf274403e1a23764bed78127", "depends": [], "constrains": ["binutils_impl_linux-64 2.40"], "license": "GPL-3.0-only", "timestamp": 1727336193000, "size": 726762, "requested_spec": "None", "package_tarball_full_path": "/root/miniconda3/pkgs/ld_impl_linux-64-2.40-h12ee557_0.conda", "extracted_package_dir": "/root/miniconda3/pkgs/ld_impl_linux-64-2.40-h12ee557_0", "files": ["bin/x86_64-conda-linux-gnu-ld", "bin/x86_64-conda_cos7-linux-gnu-ld", "x86_64-conda-linux-gnu/bin/ld", "x86_64-conda_cos7-linux-gnu/bin/ld"], "paths_data": {"paths_version": 1, "paths": [{"_path": "bin/x86_64-conda-linux-gnu-ld", "path_type": "hardlink", "sha256": "aaaab6b3200c6f71e5f2970b01a074c958d5af546e5f43c011192307f69d9cac", "size_in_bytes": 2195376, "sha256_in_prefix": "aaaab6b3200c6f71e5f2970b01a074c958d5af546e5f43c011192307f69d9cac"}, {"_path": "bin/x86_64-conda_cos7-linux-gnu-ld", "path_type": "softlink", "sha256": "aaaab6b3200c6f71e5f2970b01a074c958d5af546e5f43c011192307f69d9cac", "size_in_bytes": 2195376}, {"_path": "x86_64-conda-linux-gnu/bin/ld", "path_type": "softlink", "sha256": "aaaab6b3200c6f71e5f2970b01a074c958d5af546e5f43c011192307f69d9cac", "size_in_bytes": 2195376}, {"_path": "x86_64-conda_cos7-linux-gnu/bin/ld", "path_type": "softlink", "sha256": "aaaab6b3200c6f71e5f2970b01a074c958d5af546e5f43c011192307f69d9cac", "size_in_bytes": 2195376}]}, "link": {"source": "/root/miniconda3/pkgs/ld_impl_linux-64-2.40-h12ee557_0", "type": 1}}