{"name": "ca-certificates", "version": "2025.7.15", "build": "h06a4308_0", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "subdir": "linux-64", "fn": "ca-certificates-2025.7.15-h06a4308_0.conda", "md5": "a65eaddc4f9529b9c908f544ca50e7e0", "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/ca-certificates-2025.7.15-h06a4308_0.conda", "sha256": "8028e875861a4ce18b1b87c32dad10925cf1ec1c3da41d410282927ef6aad8be", "depends": [], "constrains": [], "license": "MPL-2.0", "timestamp": 1754645312000, "size": 128766, "requested_spec": "None", "package_tarball_full_path": "/root/miniconda3/pkgs/ca-certificates-2025.7.15-h06a4308_0.conda", "extracted_package_dir": "/root/miniconda3/pkgs/ca-certificates-2025.7.15-h06a4308_0", "files": ["ssl/cacert.pem", "ssl/cert.pem"], "paths_data": {"paths_version": 1, "paths": [{"_path": "ssl/cacert.pem", "path_type": "hardlink", "sha256": "7430e90ee0cdca2d0f02b1ece46fbf255d5d0408111f009638e3b892d6ca089c", "size_in_bytes": 222971, "sha256_in_prefix": "7430e90ee0cdca2d0f02b1ece46fbf255d5d0408111f009638e3b892d6ca089c"}, {"_path": "ssl/cert.pem", "path_type": "softlink", "sha256": "7430e90ee0cdca2d0f02b1ece46fbf255d5d0408111f009638e3b892d6ca089c", "size_in_bytes": 222971}]}, "link": {"source": "/root/miniconda3/pkgs/ca-certificates-2025.7.15-h06a4308_0", "type": 1}}