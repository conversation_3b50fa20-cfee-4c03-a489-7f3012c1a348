{"name": "libgomp", "version": "11.2.0", "build": "h1234567_1", "build_number": 1, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "subdir": "linux-64", "fn": "libgomp-11.2.0-h1234567_1.conda", "md5": "b372c0eea9b60732fdae4b817a63c8cd", "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/libgomp-11.2.0-h1234567_1.conda", "sha256": "a1c6e599df45e116af81c36ec4c9efb1793fa3a0b854dd90dd6c8813cd476e90", "depends": ["_libgcc_mutex 0.1 main"], "constrains": [], "license": "GPL-3.0-only WITH GCC-exception-3.1", "timestamp": 1654090775000, "size": 485145, "requested_spec": "None", "package_tarball_full_path": "/root/miniconda3/pkgs/libgomp-11.2.0-h1234567_1.conda", "extracted_package_dir": "/root/miniconda3/pkgs/libgomp-11.2.0-h1234567_1", "files": ["lib/libgomp.so", "lib/libgomp.so.1.0.0", "share/licenses/gcc-libs/RUNTIME.LIBRARY.EXCEPTION.gomp_copy"], "paths_data": {"paths_version": 1, "paths": [{"_path": "lib/libgomp.so", "path_type": "softlink", "sha256": "e3b68c5f37afb7b70bd12273b69706ab33a397714e8336910f0e47f8f1cf6854", "size_in_bytes": 1265616}, {"_path": "lib/libgomp.so.1.0.0", "path_type": "hardlink", "sha256": "e3b68c5f37afb7b70bd12273b69706ab33a397714e8336910f0e47f8f1cf6854", "size_in_bytes": 1265616, "sha256_in_prefix": "e3b68c5f37afb7b70bd12273b69706ab33a397714e8336910f0e47f8f1cf6854"}, {"_path": "share/licenses/gcc-libs/RUNTIME.LIBRARY.EXCEPTION.gomp_copy", "path_type": "hardlink", "sha256": "9d6b43ce4d8de0c878bf16b54d8e7a10d9bd42b75178153e3af6a815bdc90f74", "size_in_bytes": 3324, "sha256_in_prefix": "9d6b43ce4d8de0c878bf16b54d8e7a10d9bd42b75178153e3af6a815bdc90f74"}]}, "link": {"source": "/root/miniconda3/pkgs/libgomp-11.2.0-h1234567_1", "type": 1}}