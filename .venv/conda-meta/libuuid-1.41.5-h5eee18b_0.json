{"name": "libuuid", "version": "1.41.5", "build": "h5eee18b_0", "build_number": 0, "channel": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64", "subdir": "linux-64", "fn": "libuuid-1.41.5-h5eee18b_0.conda", "md5": "4a6a2354414c9080327274aa514e5299", "url": "https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/linux-64/libuuid-1.41.5-h5eee18b_0.conda", "sha256": "2a401aafabac51b7736cfe12d2ab205d29052640ea8183253c9d0a8e7ed0d49a", "depends": ["libgcc-ng >=11.2.0"], "constrains": [], "license": "BSD-3-<PERSON><PERSON>", "timestamp": 1668082729000, "size": 28110, "requested_spec": "None", "package_tarball_full_path": "/root/miniconda3/pkgs/libuuid-1.41.5-h5eee18b_0.conda", "extracted_package_dir": "/root/miniconda3/pkgs/libuuid-1.41.5-h5eee18b_0", "files": ["include/uuid/uuid.h", "lib/libuuid.a", "lib/libuuid.so", "lib/libuuid.so.1", "lib/libuuid.so.1.3.0", "lib/pkgconfig/uuid.pc"], "paths_data": {"paths_version": 1, "paths": [{"_path": "include/uuid/uuid.h", "path_type": "hardlink", "sha256": "926b9441cae3c113950827ef438cb0b07657f6ec1d2fe5f3ba557662ddbb526b", "size_in_bytes": 3910, "sha256_in_prefix": "926b9441cae3c113950827ef438cb0b07657f6ec1d2fe5f3ba557662ddbb526b"}, {"_path": "lib/libuuid.a", "path_type": "hardlink", "sha256": "d16861859d7ad6a76c11296ef77000e95f64d75330ce6365f679c1c88c0ecabd", "size_in_bytes": 53390, "sha256_in_prefix": "d16861859d7ad6a76c11296ef77000e95f64d75330ce6365f679c1c88c0ecabd"}, {"_path": "lib/libuuid.so", "path_type": "softlink", "sha256": "b42fa6cf1dcaca6b84e8155c4649d6bad561eaca2e8fa9473db178dbaa4aec53", "size_in_bytes": 35944}, {"_path": "lib/libuuid.so.1", "path_type": "softlink", "sha256": "b42fa6cf1dcaca6b84e8155c4649d6bad561eaca2e8fa9473db178dbaa4aec53", "size_in_bytes": 35944}, {"_path": "lib/libuuid.so.1.3.0", "path_type": "hardlink", "sha256": "b42fa6cf1dcaca6b84e8155c4649d6bad561eaca2e8fa9473db178dbaa4aec53", "size_in_bytes": 35944, "sha256_in_prefix": "b42fa6cf1dcaca6b84e8155c4649d6bad561eaca2e8fa9473db178dbaa4aec53"}, {"_path": "lib/pkgconfig/uuid.pc", "prefix_placeholder": "/croot/libuuid_1668082679328/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "file_mode": "text", "path_type": "hardlink", "sha256": "ad771b4cb15ca6fcc526199ebbc1c4ff31e1cf96f01fb4710b14462327ebbef1", "size_in_bytes": 1208, "sha256_in_prefix": "7b0c1ce62c6482d164216b25418dbb1540d88df349c96980598970821dafb573"}]}, "link": {"source": "/root/miniconda3/pkgs/libuuid-1.41.5-h5eee18b_0", "type": 1}}